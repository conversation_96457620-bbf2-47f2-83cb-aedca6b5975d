apiVersion: v1
kind: ConfigMap
metadata:
  name: redirection-nginx-cfg
  namespace: dcai-test
data:
  default.conf: |
    server {
        listen 80;
        server_name dcai-test.ebaas.com;

        location /consumer/ {
            proxy_pass http://cons-test.ebaas.com;
            proxy_set_header Host cons-test.ebaas.com;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redirection-nginx-dpt
  namespace: dcai-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redirection-nginx-app
  template:
    metadata:
      labels:
        app: redirection-nginx-app
    spec:
      containers:
        - name: nginx
          image: nginx:latest
          ports:
            - containerPort: 80
          volumeMounts:
            - name: redirection-nginx-cfg
              mountPath: /etc/nginx/conf.d
              readOnly: true
      volumes:
        - name: redirection-nginx-cfg
          configMap:
            name: redirection-nginx-cfg
---
# --------------- HPA自动伸缩配置 ---------------
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: redirection-nginx-hpa
  namespace: dcai-test
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: redirection-nginx-dpt
  minReplicas: 1
  maxReplicas: 10
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 75
---
apiVersion: v1
kind: Service
metadata:
  name: redirection-nginx-svc
  namespace: dcai-test
spec:
  selector:
    app: redirection-nginx-app
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP