apiVersion: v1
kind: ConfigMap
metadata:
  name: wechat-verify-cfg
  namespace: dcai-prod
data:
  QZkIVgVJh8.txt: 29ce63f55ebef5eefa5e86c5fad6df7f
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wechat-verify-dpt
  namespace: dcai-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: wechat-verify-app
  template:
    metadata:
      labels:
        app: wechat-verify-app
    spec:
      containers:
        - name: nginx
          image: nginx:latest
          volumeMounts:
            - name: config-volume
              mountPath: /usr/share/nginx/html
      volumes:
        - name: config-volume
          configMap:
            name: wechat-verify-cfg
---
apiVersion: v1
kind: Service
metadata:
  name: wechat-verify-svc
  namespace: dcai-prod
spec:
  selector:
    app: wechat-verify-app
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: wechat-verify-igr
  namespace: dcai-prod
  annotations:
    mse.aliyun.com/service-type: "MSE"  # 声明使用MSE网关
spec:
  ingressClassName: mse
  tls:
    - hosts:
        - dcai.ebaas.com
      secretName: dcai.ebaas.com-tls-secret
  rules:
    - host: dcai.ebaas.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: wechat-verify-svc
                port:
                  number: 80